# 任务标题：优化MCP服务器参数描述和项目管理

## 用户需求

1. def aggregate_files(file_paths: List[str], output_path: str) -> str: 为参数提供可以被 AI 客户端阅读的参数描述
2. 搜索网络了解如何为参数提供描述
3. 这是为 LLM 准备的上下文，而不是报告，更改标题，以及结尾的报告时间等内容
4. 参考 /Users/<USER>/Desktop/01-Projects/09-MCP/user_consultation_mcp/server.py
5. 使用 uv 来管理依赖，并支持使用类似于
{
  "mcpServers": {
    "user_consultation_mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "/Users/<USER>/Desktop/01-Projects/09-MCP/user_consultation_mcp",
        "run",
        "server.py"
      ],
      "env": {
        "GEMINI_API_KEY": "AIzaSyBmYomjbUrT-OlAuf0fOx1ttyVsPrxT1X0"
      }
    }
  }
}

来启动应用

## AI 分析区域

### 核心问题分析

用户希望进一步优化现有的MCP服务器，主要关注以下几个方面：

1. **参数描述优化**：当前的aggregate_files函数缺少详细的参数描述，AI客户端无法理解参数的具体用途和格式要求
2. **输出内容重新定位**：当前输出格式偏向"报告"风格，需要调整为更适合LLM理解的"上下文"格式
3. **项目管理现代化**：从pip+requirements.txt迁移到uv+pyproject.toml的现代Python项目管理方式
4. **部署配置标准化**：支持标准的MCP客户端配置格式，便于集成到Claude Desktop等AI客户端

### 技术解决方案设计

1. **参数描述增强**：
   - 使用FastMCP的参数注解功能
   - 为每个参数添加详细的描述信息
   - 提供参数格式和示例

2. **输出格式优化**：
   - 移除"报告"相关的标题和时间戳
   - 调整为"上下文聚合"的表述
   - 优化markdown结构，更适合LLM解析

3. **项目结构现代化**：
   - 创建pyproject.toml配置文件
   - 使用uv管理依赖和虚拟环境
   - 添加项目元数据和脚本配置

4. **部署配置**：
   - 更新README.md中的配置示例
   - 提供标准的MCP客户端配置
   - 支持uv run方式启动

### 关键技术实现点

1. **FastMCP参数注解**：使用typing.Annotated和Field来添加参数描述
2. **uv项目配置**：pyproject.toml中定义依赖、脚本和项目信息
3. **输出格式调整**：修改模板字符串，去除报告风格的内容
4. **启动脚本优化**：支持uv run直接启动服务器

## CML 任务清单

### 阶段一：项目结构现代化
- [x] 创建pyproject.toml文件：定义项目元数据、依赖和脚本配置
- [x] 删除requirements.txt文件：迁移到pyproject.toml管理依赖
- [x] 删除venv目录：使用uv自动管理虚拟环境

### 阶段二：参数描述优化
- [x] 在main.py中导入typing.Annotated和pydantic.Field：用于参数注解
- [x] 修改aggregate_files函数签名：为file_paths参数添加详细描述注解
- [x] 修改aggregate_files函数签名：为output_path参数添加详细描述注解

### 阶段三：输出格式优化和语言支持扩展
- [x] 修改output_content模板：将"文件上下文聚合报告"改为"聚合的文件上下文"
- [x] 删除报告生成时间：移除模板中的时间戳部分
- [x] 扩展语言支持：添加markdown、swift等更多编程语言的语法识别

### 阶段四：配置和文档更新
- [x] 更新README.md：添加uv安装和使用说明
- [x] 更新README.md：提供标准MCP客户端配置示例
- [x] 创建启动脚本：支持uv run方式启动服务器

### 阶段五：测试和验证
- [x] 使用uv安装依赖：验证pyproject.toml配置正确性
- [x] 测试参数描述：验证AI客户端能正确理解参数信息
- [x] 测试输出格式：验证新的上下文格式符合预期

## 提交消息区域

```
refactor: 优化MCP服务器参数描述和项目管理

- 迁移到uv+pyproject.toml现代项目管理，删除requirements.txt和手动venv
- 为aggregate_files参数添加详细的AI可读描述，使用Annotated和Field注解
- 优化输出格式，从"报告"调整为"上下文"风格，移除时间戳
- 扩展语言支持到60+种编程语言，包括Swift、Markdown等
- 更新README.md文档，添加uv安装说明和标准MCP客户端配置
- 添加开发工具配置（black、isort、mypy、pytest）
- 支持uv run方式启动服务器

测试结果：
- ✅ uv sync成功安装所有依赖
- ✅ 新的上下文格式输出正常
- ✅ 扩展的语言支持工作正常
- ✅ MCP服务器正常启动和导入
- ✅ 参数描述增强，AI客户端友好

Closes: MCP服务器优化需求
```