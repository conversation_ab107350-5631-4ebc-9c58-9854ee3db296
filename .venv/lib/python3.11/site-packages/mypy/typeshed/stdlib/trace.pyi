import sys
import types
from _typeshed import Incomplete, StrPath, TraceFunction
from collections.abc import Callable, Iterable, Mapping, Sequence
from typing import Any, TypeVar
from typing_extensions import ParamSpec, TypeAlias

__all__ = ["Trace", "CoverageResults"]

_T = TypeVar("_T")
_P = ParamSpec("_P")
_FileModuleFunction: TypeAlias = tuple[str, str | None, str]

class CoverageResults:
    counts: dict[tuple[str, int], int]
    counter: dict[tuple[str, int], int]
    calledfuncs: dict[_FileModuleFunction, int]
    callers: dict[tuple[_FileModuleFunction, _FileModuleFunction], int]
    inifile: StrPath | None
    outfile: StrPath | None
    def __init__(
        self,
        counts: dict[tuple[str, int], int] | None = None,
        calledfuncs: dict[_FileModuleFunction, int] | None = None,
        infile: StrPath | None = None,
        callers: dict[tuple[_FileModuleFunction, _FileModuleFunction], int] | None = None,
        outfile: StrPath | None = None,
    ) -> None: ...  # undocumented
    def update(self, other: CoverageResults) -> None: ...
    if sys.version_info >= (3, 13):
        def write_results(
            self,
            show_missing: bool = True,
            summary: bool = False,
            coverdir: StrPath | None = None,
            *,
            ignore_missing_files: bool = False,
        ) -> None: ...
    else:
        def write_results(self, show_missing: bool = True, summary: bool = False, coverdir: StrPath | None = None) -> None: ...

    def write_results_file(
        self, path: StrPath, lines: Sequence[str], lnotab: Any, lines_hit: Mapping[int, int], encoding: str | None = None
    ) -> tuple[int, int]: ...
    def is_ignored_filename(self, filename: str) -> bool: ...  # undocumented

class _Ignore:
    def __init__(self, modules: Iterable[str] | None = None, dirs: Iterable[StrPath] | None = None) -> None: ...
    def names(self, filename: str, modulename: str) -> int: ...

class Trace:
    inifile: StrPath | None
    outfile: StrPath | None
    ignore: _Ignore
    counts: dict[str, int]
    pathtobasename: dict[Incomplete, Incomplete]
    donothing: int
    trace: int
    start_time: int | None
    globaltrace: TraceFunction
    localtrace: TraceFunction
    def __init__(
        self,
        count: int = 1,
        trace: int = 1,
        countfuncs: int = 0,
        countcallers: int = 0,
        ignoremods: Sequence[str] = (),
        ignoredirs: Sequence[str] = (),
        infile: StrPath | None = None,
        outfile: StrPath | None = None,
        timing: bool = False,
    ) -> None: ...
    def run(self, cmd: str | types.CodeType) -> None: ...
    def runctx(
        self, cmd: str | types.CodeType, globals: Mapping[str, Any] | None = None, locals: Mapping[str, Any] | None = None
    ) -> None: ...
    def runfunc(self, func: Callable[_P, _T], /, *args: _P.args, **kw: _P.kwargs) -> _T: ...
    def file_module_function_of(self, frame: types.FrameType) -> _FileModuleFunction: ...
    def globaltrace_trackcallers(self, frame: types.FrameType, why: str, arg: Any) -> None: ...
    def globaltrace_countfuncs(self, frame: types.FrameType, why: str, arg: Any) -> None: ...
    def globaltrace_lt(self, frame: types.FrameType, why: str, arg: Any) -> None: ...
    def localtrace_trace_and_count(self, frame: types.FrameType, why: str, arg: Any) -> TraceFunction: ...
    def localtrace_trace(self, frame: types.FrameType, why: str, arg: Any) -> TraceFunction: ...
    def localtrace_count(self, frame: types.FrameType, why: str, arg: Any) -> TraceFunction: ...
    def results(self) -> CoverageResults: ...
