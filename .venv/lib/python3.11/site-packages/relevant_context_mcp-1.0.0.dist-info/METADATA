Metadata-Version: 2.4
Name: relevant-context-mcp
Version: 1.0.0
Summary: MCP服务器用于聚合多个文件的内容到统一的上下文中
Project-URL: Homepage, https://github.com/user/relevant-context-mcp
Project-URL: Repository, https://github.com/user/relevant-context-mcp
Project-URL: Issues, https://github.com/user/relevant-context-mcp/issues
Author-email: User <<EMAIL>>
License: MIT
Keywords: aggregation,ai,context,llm,mcp
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.10
Requires-Dist: mcp>=1.12.0
Provides-Extra: dev
Requires-Dist: black>=23.0.0; extra == 'dev'
Requires-Dist: isort>=5.0.0; extra == 'dev'
Requires-Dist: mypy>=1.0.0; extra == 'dev'
Requires-Dist: pytest>=7.0.0; extra == 'dev'
Description-Content-Type: text/markdown

# 文件上下文聚合MCP服务器

这是一个基于Model Context Protocol (MCP)框架开发的Python服务器，用于聚合多个文件的内容到一个统一的上下文文件中。它能够自动计算文件的共同祖先路径，生成文件树结构，并以专为LLM优化的markdown格式输出。

## 功能特性

- **智能路径处理**：自动计算多个文件的共同祖先路径
- **文件树生成**：基于共同祖先路径生成清晰的文件结构树
- **广泛语言支持**：支持60+种编程语言和文件格式的语法高亮
- **LLM优化输出**：专为AI模型设计的上下文格式，便于理解和处理
- **参数智能描述**：AI客户端可读的详细参数说明
- **错误处理**：完善的错误处理机制，友好的错误提示
- **现代项目管理**：使用uv进行依赖管理和虚拟环境
- **MCP标准**：完全符合MCP协议标准，可与各种AI客户端集成

## 支持的编程语言

### 主流编程语言
- **Python**: .py, .pyx, .pyi
- **JavaScript/TypeScript**: .js, .jsx, .ts, .tsx, .mjs, .cjs
- **Swift**: .swift (iOS/macOS开发)
- **Java/Kotlin**: .java, .kt (Android开发)
- **C/C++**: .c, .cpp, .cc, .cxx, .h, .hpp
- **Rust**: .rs
- **Go**: .go

### Web技术
- **HTML/CSS**: .html, .htm, .css, .scss, .sass, .less
- **Markdown**: .md, .markdown

### 配置和数据格式
- **JSON/YAML**: .json, .yaml, .yml
- **TOML/INI**: .toml, .ini, .cfg, .conf
- **XML/CSV**: .xml, .csv

### 脚本和其他
- **Shell**: .sh, .bash, .zsh, .fish
- **SQL**: .sql, .sqlite, .psql
- **Ruby/PHP**: .rb, .php
- **更多**: 支持60+种语言格式

## 安装和设置

### 前置要求
- Python 3.10 或更高版本
- uv 包管理器

### 1. 安装uv（如果尚未安装）

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 或使用pip
pip install uv
```

### 2. 安装项目依赖

```bash
# 克隆或下载项目后，在项目目录中运行
uv sync
```

### 3. 运行服务器

```bash
# 使用uv运行
uv run main.py

# 或者激活虚拟环境后运行
uv shell
python main.py
```

### 4. 配置MCP客户端

在您的MCP客户端配置文件中添加以下配置：

#### 使用uv运行（推荐）
```json
{
  "mcpServers": {
    "file-context-aggregator": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/file-context-aggregator",
        "run",
        "main.py"
      ],
      "env": {}
    }
  }
}
```

#### 传统Python运行
```json
{
  "mcpServers": {
    "file-context-aggregator": {
      "command": "python",
      "args": ["/path/to/file-context-aggregator/main.py"],
      "env": {}
    }
  }
}
```

## 使用方法

### 工具：aggregate_files

这个MCP服务器提供一个工具`aggregate_files`，用于聚合多个文件的内容到统一的上下文中。

**参数：**
- `file_paths` (List[str]): 要聚合的文件路径列表。每个路径必须是绝对路径，指向存在的文本文件。支持多种文件类型如.py, .js, .md, .json, .swift等。
- `output_path` (str): 输出文件的绝对路径，用于保存聚合后的内容。文件将以markdown格式保存，包含文件树结构和所有文件内容。

**示例使用：**

```bash
# 使用uv运行并测试工具
uv run mcp tools main.py

# 调用aggregate_files工具
uv run mcp call aggregate_files --params '{
  "file_paths": [
    "/path/to/file1.py",
    "/path/to/file2.swift",
    "/path/to/config.json",
    "/path/to/README.md"
  ],
  "output_path": "/path/to/output/context.md"
}' main.py
```

### 输出格式

生成的输出文件专为LLM优化，包含以下部分：

1. **文件结构**：显示所有文件基于共同根路径的相对位置
2. **文件内容**：每个文件的完整内容，支持60+种语言的语法高亮

示例输出：

```markdown
# 聚合的文件上下文

## 文件结构

共同根路径: `/Users/<USER>

```
├── src/utils.py
├── mobile/ViewController.swift
├── config.json
└── docs/README.md
```

## 文件内容

### src/utils.py

```python
def hello_world():
    print("Hello, World!")
```

### mobile/ViewController.swift

```swift
import UIKit

class ViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        print("Hello, Swift!")
    }
}
```

### config.json

```json
{
  "name": "example",
  "version": "1.0.0"
}
```

### docs/README.md

```markdown
# Project Documentation

This is a sample project.
```
```

## 测试

项目包含测试文件用于验证功能：

```bash
# 使用uv运行测试
uv run python test_aggregation.py

# 或者手动测试聚合功能
uv run python -c "
from main import aggregate_files

result = aggregate_files([
    'test_files/src/utils.py',
    'test_files/config.json',
    'test_files/docs/README.md'
], 'test_output.md')
print(result)
"
```

## 开发

### 安装开发依赖

```bash
# 安装开发工具
uv add --dev pytest black isort mypy
```

### 代码格式化

```bash
# 格式化代码
uv run black .
uv run isort .

# 类型检查
uv run mypy main.py
```

### 运行测试

```bash
# 运行测试套件
uv run pytest
```

## 错误处理

服务器包含完善的错误处理机制：

- 文件不存在检查
- 权限验证
- 编码错误处理
- 二进制文件检测

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
