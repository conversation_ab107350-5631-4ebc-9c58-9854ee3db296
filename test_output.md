# 聚合的文件上下文

## 文件结构

共同根路径: `/Users/<USER>/Desktop/01-Projects/09-MCP/relevant_context_mcp/test_files`

```
├── config.json
  ├── README.md
  ├── utils.py
```

## 文件内容

### config.json

```json
{
  "app_name": "文件聚合器",
  "version": "1.0.0",
  "settings": {
    "encoding": "utf-8",
    "max_file_size": "10MB"
  }
}

```
### docs/README.md

```markdown
# 测试文档

这是一个用于测试MCP文件聚合功能的示例文档。

## 功能特性

- 支持多文件聚合
- 自动生成文件树
- Markdown格式输出

```
### src/utils.py

```python
"""
工具函数模块
"""

def calculate_sum(a: int, b: int) -> int:
    """计算两个数的和"""
    return a + b

def format_message(name: str, message: str) -> str:
    """格式化消息"""
    return f"Hello {name}: {message}"

```
