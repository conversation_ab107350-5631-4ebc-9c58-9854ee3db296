import UIKit

class ViewController: UIViewController {
    
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var messageLabel: UILabel!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        titleLabel.text = "文件上下文聚合器"
        messageLabel.text = "支持Swift语言高亮显示"
        
        view.backgroundColor = .systemBackground
    }
    
    @IBAction func aggregateButtonTapped(_ sender: UIButton) {
        print("开始聚合文件上下文...")
        // 调用MCP服务器聚合文件
    }
}
