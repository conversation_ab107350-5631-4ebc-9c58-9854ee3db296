# 聚合的文件上下文

## 文件结构

共同根路径: `/Users/<USER>/Desktop/01-Projects/09-MCP/relevant_context_mcp/test_files`

```
├── config.json
  ├── README.md
  ├── ViewController.swift
  ├── utils.py
```

## 文件内容

### config.json

```json
{
  "app_name": "文件聚合器",
  "version": "1.0.0",
  "settings": {
    "encoding": "utf-8",
    "max_file_size": "10MB"
  }
}

```
### docs/README.md

```markdown
# 测试文档

这是一个用于测试MCP文件聚合功能的示例文档。

## 功能特性

- 支持多文件聚合
- 自动生成文件树
- Markdown格式输出

```
### mobile/ViewController.swift

```swift
import UIKit

class ViewController: UIViewController {
    
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var messageLabel: UILabel!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        titleLabel.text = "文件上下文聚合器"
        messageLabel.text = "支持Swift语言高亮显示"
        
        view.backgroundColor = .systemBackground
    }
    
    @IBAction func aggregateButtonTapped(_ sender: UIButton) {
        print("开始聚合文件上下文...")
        // 调用MCP服务器聚合文件
    }
}

```
### src/utils.py

```python
"""
工具函数模块
"""

def calculate_sum(a: int, b: int) -> int:
    """计算两个数的和"""
    return a + b

def format_message(name: str, message: str) -> str:
    """格式化消息"""
    return f"Hello {name}: {message}"

```
